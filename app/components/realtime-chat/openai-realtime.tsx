import VoiceIcon from "@/app/icons/voice.svg";
import VoiceOffIcon from "@/app/icons/voice-off.svg";
import PowerIcon from "@/app/icons/power.svg";

import styles from "./realtime-chat.module.scss";
import clsx from "clsx";

import { useState, useRef, useEffect } from "react";

import { useChatStore, createMessage, useAppConfig } from "@/app/store";

import { IconButton } from "@/app/components/button";
import { VoicePrint } from "@/app/components/voice-print";

interface OpenAIRealtimeProps {
  onClose?: () => void;
  onStartVoice?: () => void;
  onPausedVoice?: () => void;
}

export function OpenAIRealtime(props: OpenAIRealtimeProps) {
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [error, setError] = useState<string | null>(null);

  console.log("OpenAIRealtime component rendered");

  const pcRef = useRef<RTCPeerConnection | null>(null);
  const dcRef = useRef<RTCDataChannel | null>(null);
  const audioElementRef = useRef<HTMLAudioElement | null>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);

  const config = useAppConfig();
  const chatStore = useChatStore();

  const apiKey = config.realtimeConfig.apiKey;
  const model = config.realtimeConfig.model;
  const voice = config.realtimeConfig.voice;
  const temperature = config.realtimeConfig.temperature;
  const baseUrl = config.realtimeConfig.baseUrl;
  const modalities = config.realtimeConfig.modalities || ["text", "audio"];
  const vadEnabled = config.realtimeConfig.vadEnabled ?? true;

  // 创建临时API密钥（如果需要）
  const createEphemeralKey = async () => {
    try {
      // 如果使用自定义baseUrl，构建完整的sessions URL
      const sessionsUrl = baseUrl 
        ? `${baseUrl}/v1/realtime/sessions`
        : "https://api.openai.com/v1/realtime/sessions";

      const response = await fetch(sessionsUrl, {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          model,
          voice,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to create ephemeral key: ${response.statusText}`);
      }

      const data = await response.json();
      return data.client_secret.value;
    } catch (error) {
      console.error("Error creating ephemeral key:", error);
      // 如果创建临时密钥失败，直接使用原始API密钥
      return apiKey;
    }
  };

  const handleConnect = async () => {
    if (isConnecting) return;
    
    try {
      setIsConnecting(true);
      setError(null);

      // 获取临时API密钥
      const ephemeralKey = await createEphemeralKey();

      // 创建RTCPeerConnection
      const pc = new RTCPeerConnection();
      pcRef.current = pc;

      // 设置音频播放
      const audioEl = document.createElement("audio");
      audioEl.autoplay = true;
      audioElementRef.current = audioEl;
      
      pc.ontrack = (e) => {
        console.log("Received remote audio track");
        audioEl.srcObject = e.streams[0];
      };

      // 获取用户麦克风
      try {
        const mediaStream = await navigator.mediaDevices.getUserMedia({
          audio: true
        });
        mediaStreamRef.current = mediaStream;
        pc.addTrack(mediaStream.getTracks()[0]);
        console.log("Added local audio track");
      } catch (err) {
        console.error("Failed to get user media:", err);
        throw new Error("无法访问麦克风，请检查权限设置");
      }

      // 设置数据通道
      const dc = pc.createDataChannel("oai-events");
      dcRef.current = dc;
      
      dc.addEventListener("message", (e) => {
        try {
          const serverEvent = JSON.parse(e.data);
          handleServerEvent(serverEvent);
        } catch (err) {
          console.error("Error parsing server event:", err);
        }
      });

      dc.addEventListener("open", () => {
        console.log("Data channel opened");
        // 配置会话
        configureSession();
      });

      // 创建offer并设置本地描述
      const offer = await pc.createOffer();
      await pc.setLocalDescription(offer);

      // 发送SDP到OpenAI
      const realtimeUrl = baseUrl 
        ? `${baseUrl}/v1/realtime`
        : "https://api.openai.com/v1/realtime";

      const sdpResponse = await fetch(`${realtimeUrl}?model=${model}`, {
        method: "POST",
        body: offer.sdp,
        headers: {
          Authorization: `Bearer ${ephemeralKey}`,
          "Content-Type": "application/sdp"
        },
      });

      if (!sdpResponse.ok) {
        throw new Error(`SDP exchange failed: ${sdpResponse.statusText}`);
      }

      const answer = {
        type: "answer" as RTCSdpType,
        sdp: await sdpResponse.text(),
      };

      await pc.setRemoteDescription(answer);
      
      setIsConnected(true);
      console.log("WebRTC connection established");

    } catch (error) {
      console.error("Connection failed:", error);
      setError(error instanceof Error ? error.message : "连接失败");
      cleanup();
    } finally {
      setIsConnecting(false);
    }
  };

  const configureSession = () => {
    if (!dcRef.current) return;

    const sessionConfig = {
      type: "session.update",
      session: {
        instructions: "",
        voice,
        input_audio_transcription: { model: "whisper-1" },
        turn_detection: vadEnabled ? { type: "server_vad" } : null,
        tools: [],
        temperature,
        modalities,
      },
    };

    dcRef.current.send(JSON.stringify(sessionConfig));
    console.log("Session configured");
  };

  const handleServerEvent = (event: any) => {
    console.log("Server event:", event.type, event);

    switch (event.type) {
      case "session.created":
        console.log("Session created");
        break;
      case "session.updated":
        console.log("Session updated");
        break;
      case "input_audio_buffer.speech_started":
        setIsRecording(true);
        props.onStartVoice?.();
        break;
      case "input_audio_buffer.speech_stopped":
        setIsRecording(false);
        props.onPausedVoice?.();
        break;
      case "response.text.delta":
        // 处理文本增量
        if (event.delta) {
          // 可以在这里更新UI显示实时文本
          console.log("Text delta:", event.delta);
        }
        break;
      case "response.audio.delta":
        // 音频数据通过WebRTC自动处理
        break;
      case "response.done":
        console.log("Response completed:", event.response);
        // 可以在这里添加到聊天历史
        if (event.response.output) {
          event.response.output.forEach((item: any) => {
            if (item.type === "message" && item.content) {
              const textContent = item.content
                .filter((c: any) => c.type === "text")
                .map((c: any) => c.text)
                .join("");
              
              if (textContent) {
                const message = createMessage({
                  role: "assistant",
                  content: textContent,
                });
                const currentSession = chatStore.currentSession();
                chatStore.updateTargetSession(currentSession, (session) => {
                  session.messages = session.messages.concat([message]);
                });
              }
            }
          });
        }
        break;
      case "error":
        console.error("Server error:", event);
        setError(`服务器错误: ${event.message}`);
        break;
    }
  };

  const handleDisconnect = () => {
    cleanup();
    setIsConnected(false);
    props.onClose?.();
  };

  const cleanup = () => {
    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach(track => track.stop());
      mediaStreamRef.current = null;
    }

    if (pcRef.current) {
      pcRef.current.close();
      pcRef.current = null;
    }

    if (audioElementRef.current) {
      audioElementRef.current.srcObject = null;
      audioElementRef.current = null;
    }

    dcRef.current = null;
    setIsRecording(false);
  };

  useEffect(() => {
    return () => {
      cleanup();
    };
  }, []);

  return (
    <div className={styles["realtime-chat"]}>
      <div className={styles["realtime-header"]}>
        <div className={styles["realtime-title"]}>
          🎙️ 实时语音对话
        </div>
        <IconButton
          icon={<PowerIcon />}
          onClick={handleDisconnect}
          className={styles["realtime-close"]}
        />
      </div>

      <div className={styles["realtime-content"]}>
        {error && (
          <div className={styles["realtime-error"]}>
            ❌ {error}
          </div>
        )}

        <div className={styles["realtime-status"]}>
          {isConnecting && "🔄 正在连接..."}
          {isConnected && !isRecording && "🎤 准备就绪，开始说话"}
          {isConnected && isRecording && "🔴 正在录音..."}
          {!isConnected && !isConnecting && "⚪ 未连接"}
        </div>

        {isConnected && (
          <div className={styles["realtime-visualizer"]}>
            <VoicePrint isActive={isRecording} />
          </div>
        )}

        <div className={styles["realtime-controls"]}>
          {!isConnected ? (
            <IconButton
              icon={<VoiceIcon />}
              onClick={handleConnect}
              disabled={isConnecting}
              className={clsx(styles["realtime-button"], styles["connect"])}
              text={isConnecting ? "连接中..." : "开始语音对话"}
            />
          ) : (
            <IconButton
              icon={<VoiceOffIcon />}
              onClick={handleDisconnect}
              className={clsx(styles["realtime-button"], styles["disconnect"])}
              text="结束对话"
            />
          )}
        </div>

        <div className={styles["realtime-info"]}>
          <div>模型: {model}</div>
          <div>音色: {voice}</div>
          <div>模式: {modalities.join(" + ")}</div>
          {baseUrl && <div>自定义URL: {baseUrl}</div>}
        </div>
      </div>
    </div>
  );
}
