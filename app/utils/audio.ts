type TTSPlayer = {
  init: () => void;
  play: (audioBuffer: ArrayBuffer, onended: () => void | null) => Promise<void>;
  stop: () => void;
};

export function createTTSPlayer(): TTSPlayer {
  let audioContext: AudioContext | null = null;
  let audioBufferSourceNode: AudioBufferSourceNode | null = null;

  const init = () => {
    console.log("[Audio Player Debug] 🎵 初始化音频上下文");
    audioContext = new (window.AudioContext || window.webkitAudioContext)();
    console.log("[Audio Player Debug] 🎵 音频上下文状态:", audioContext.state);
    audioContext.suspend();
    console.log("[Audio Player Debug] 🎵 音频上下文已暂停");
  };

  const play = async (audioBuffer: ArrayBuffer, onended: () => void | null) => {
    console.log("[Audio Player Debug] 🎵 开始播放音频");
    console.log("[Audio Player Debug] 🎵 音频数据大小:", audioBuffer.byteLength, "bytes");

    if (audioBufferSourceNode) {
      console.log("[Audio Player Debug] 🎵 停止之前的音频");
      audioBufferSourceNode.stop();
      audioBufferSourceNode.disconnect();
    }

    try {
      console.log("[Audio Player Debug] 🎵 解码音频数据...");
      const buffer = await audioContext!.decodeAudioData(audioBuffer);
      console.log("[Audio Player Debug] 🎵 音频解码成功:");
      console.log("[Audio Player Debug] 🎵 - 时长:", buffer.duration, "秒");
      console.log("[Audio Player Debug] 🎵 - 采样率:", buffer.sampleRate, "Hz");
      console.log("[Audio Player Debug] 🎵 - 声道数:", buffer.numberOfChannels);

      audioBufferSourceNode = audioContext!.createBufferSource();
      audioBufferSourceNode.buffer = buffer;
      audioBufferSourceNode.connect(audioContext!.destination);

      audioBufferSourceNode.onended = () => {
        console.log("[Audio Player Debug] 🎵 音频播放结束");
        if (onended) onended();
      };

      console.log("[Audio Player Debug] 🎵 恢复音频上下文并开始播放");
      await audioContext!.resume();
      console.log("[Audio Player Debug] 🎵 音频上下文状态:", audioContext!.state);
      audioBufferSourceNode!.start();
      console.log("[Audio Player Debug] 🎵 音频开始播放");
    } catch (decodeError: any) {
      console.error("[Audio Player Debug] ❌ 音频解码失败:", decodeError);
      console.error("[Audio Player Debug] ❌ 错误详情:", {
        name: decodeError?.name,
        message: decodeError?.message,
        code: decodeError?.code
      });

      // 检查音频数据的格式
      const uint8Array = new Uint8Array(audioBuffer);
      const firstBytes = Array.from(uint8Array.slice(0, 16)).map(b => b.toString(16).padStart(2, '0')).join(' ');
      console.error("[Audio Player Debug] ❌ 音频数据前16字节 (hex):", firstBytes);

      // 尝试检测文件格式
      const textDecoder = new TextDecoder();
      const firstChars = textDecoder.decode(uint8Array.slice(0, 100));
      console.error("[Audio Player Debug] ❌ 数据前100字符 (text):", firstChars);

      throw decodeError;
    }
  };

  const stop = () => {
    console.log("[Audio Player Debug] 🎵 停止音频播放");
    if (audioBufferSourceNode) {
      audioBufferSourceNode.stop();
      audioBufferSourceNode.disconnect();
      audioBufferSourceNode = null;
    }
    if (audioContext) {
      audioContext.close();
      audioContext = null;
    }
    console.log("[Audio Player Debug] 🎵 音频播放器已清理");
  };

  return { init, play, stop };
}
